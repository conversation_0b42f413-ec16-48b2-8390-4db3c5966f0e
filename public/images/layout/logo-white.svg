<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="129e43a0-5c9b-4066-8363-740202dfe3bd"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="eeea416b-acfb-4803-bb8b-99700f998466"  >
</g>
<g transform="matrix(1 0 0 1 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(0.13 0 0 -0.13 -0.29 -190.67)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5583.38, -7352.5)" d="M 5611 9525 C 5608 9414 5580 8949 5574 8910 C 5550 8730 5306 8540 4845 8341 C 4680 8269 4461 8157 4366 8096 C 4278 8039 4098 7901 4085 7881 C 4081 7873 4296 7870 4799 7870 L 5520 7870 L 5520 7828 L 5520 7785 L 4752 7788 L 3984 7791 L 3904 7708 C 3756 7553 3637 7376 3545 7170 C 3491 7050 3430 6877 3430 6843 L 3430 6820 L 3883 6820 C 4331 6821 4335 6821 4338 6800 C 4341 6780 4339 6780 3871 6780 L 3401 6780 L 3391 6728 C 3385 6699 3376 6648 3369 6615 C 3355 6540 3346 6140 3357 6080 L 3366 6035 L 3540 6032 C 3636 6030 3775 6032 3848 6035 L 3980 6042 L 3980 6207 C 3980 6399 3982 6405 4067 6464 L 4123 6503 L 4177 6475 C 4270 6427 4274 6414 4278 6207 L 4281 6028 L 4411 6031 L 4540 6034 L 4540 6195 C 4540 6387 4546 6405 4633 6465 L 4691 6504 L 4734 6481 C 4758 6469 4791 6445 4806 6429 L 4835 6399 L 4838 6216 L 4841 6033 L 4938 6031 C 4991 6030 5036 6028 5037 6026 C 5039 6024 5035 5999 5029 5970 C 5000 5833 5059 5676 5165 5615 C 5188 5601 5210 5590 5213 5590 C 5217 5590 5220 5674 5220 5776 C 5220 5994 5223 6003 5310 6049 L 5366 6079 L 5418 6050 C 5446 6033 5473 6012 5478 6002 C 5483 5993 5490 5838 5493 5658 L 5498 5330 L 4703 5328 L 3908 5325 L 3955 5255 C 3981 5217 4013 5175 4027 5163 L 4051 5140 L 5589 5140 L 7126 5140 L 7161 5188 C 7210 5253 7250 5313 7250 5322 C 7250 5327 6892 5330 6455 5330 L 5660 5330 L 5660 5643 C 5660 6001 5661 6003 5742 6054 C 5785 6080 5793 6081 5819 6070 C 5861 6053 5912 6013 5927 5987 C 5935 5972 5939 5908 5940 5783 C 5940 5683 5943 5599 5948 5597 C 5973 5582 6083 5679 6114 5744 C 6140 5798 6154 5915 6142 5978 L 6133 6030 L 6241 6030 L 6348 6030 L 6352 6203 C 6356 6402 6357 6405 6447 6465 L 6504 6503 L 6537 6485 C 6580 6461 6633 6407 6642 6378 C 6645 6365 6648 6284 6649 6198 C 6650 6079 6653 6040 6663 6040 C 6669 6039 6729 6038 6795 6037 L 6915 6035 L 6920 6216 C 6926 6420 6923 6413 7007 6467 L 7060 6500 L 7098 6481 C 7143 6458 7189 6414 7201 6382 C 7206 6369 7210 6285 7210 6195 L 7210 6031 L 7505 6033 L 7799 6035 L 7807 6090 C 7826 6233 7808 6572 7772 6740 L 7764 6775 L 7332 6778 C 6908 6780 6900 6781 6900 6800 C 6900 6820 6907 6820 7320 6820 C 7677 6820 7740 6822 7740 6834 C 7740 6869 7643 7126 7585 7245 C 7492 7436 7382 7597 7256 7728 L 7195 7791 L 6419 7788 C 5823 7786 5644 7788 5647 7797 C 5650 7803 5633 7820 5611 7834 C 5588 7849 5570 7864 5570 7868 C 5570 7872 5915 7874 6337 7872 L 7103 7870 L 7084 7891 C 7051 7928 6892 8048 6798 8106 C 6702 8165 6647 8193 6280 8374 C 5950 8535 5814 8629 5709 8768 C 5634 8867 5630 8890 5629 9204 C 5629 9353 5625 9495 5621 9520 L 5613 9565 L 5611 9525 z M 6630 6821 C 6630 6816 6633 6805 6636 6796 C 6641 6782 6626 6780 6501 6780 C 6368 6780 6360 6781 6360 6800 C 6360 6818 6368 6820 6463 6823 C 6519 6824 6580 6827 6598 6828 C 6615 6829 6630 6826 6630 6821 z M 4880 6800 C 4880 6781 4873 6780 4740 6780 C 4607 6780 4600 6781 4600 6800 C 4600 6819 4607 6820 4740 6820 C 4873 6820 4880 6819 4880 6800 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -253.22 -171.94)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3686.39, -7212.01)" d="M 4690 8911 C 4228 8760 3806 8504 3471 8172 C 3193 7896 2960 7560 2810 7216 C 2609 6758 2517 6266 2549 5811 C 2556 5714 2567 5599 2573 5555 L 2585 5475 L 2695 5475 C 2799 5475 2805 5476 2803 5495 C 2802 5506 2794 5571 2786 5640 C 2757 5867 2769 6247 2810 6475 C 2934 7153 3330 7812 3855 8215 C 4142 8435 4497 8625 4773 8704 L 4830 8721 L 4830 8835 C 4830 8921 4827 8950 4818 8949 C 4811 8949 4753 8932 4690 8911 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 258.41 -171.5)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7523.61, -7208.76)" d="M 6402 8839 L 6405 8728 L 6500 8695 C 6734 8615 6971 8495 7195 8342 C 7626 8048 7970 7632 8175 7157 C 8347 6761 8413 6429 8413 5975 C 8413 5761 8403 5645 8374 5502 L 8367 5469 L 8481 5472 L 8595 5475 L 8607 5535 C 8667 5831 8658 6252 8584 6600 C 8469 7137 8238 7610 7897 8005 C 7541 8418 7082 8726 6540 8914 C 6390 8967 6399 8971 6402 8839 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 337.99 118.66)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8120.48, -5032.5)" d="M 7524 5066 C 7521 5058 7520 5037 7522 5018 L 7525 4985 L 7643 4985 C 7707 4985 7976 4986 8240 4988 L 8720 4990 L 8720 5035 L 8720 5080 L 8125 5080 C 7623 5080 7529 5078 7524 5066 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 2.55 147.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5604.69, -4819.13)" d="M 4197 5038 C 4174 5024 4400 4831 4522 4762 C 4606 4714 4762 4651 4868 4622 L 4965 4596 L 5630 4595 L 6295 4595 L 6385 4629 C 6603 4710 6831 4851 6960 4984 L 7014 5040 L 6100 5040 C 5597 5041 4965 5042 4697 5043 C 4428 5044 4203 5042 4197 5038 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -342.74 141)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3015, -4865)" d="M 2410 4865 L 2410 4820 L 3015 4820 L 3620 4820 L 3620 4865 L 3620 4910 L 3015 4910 L 2410 4910 L 2410 4865 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 392.13 138.41)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8526.52, -4884.38)" d="M 8508 4883 C 8520 4881 8538 4881 8548 4883 C 8557 4886 8547 4888 8525 4887 C 8503 4887 8495 4885 8508 4883 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 415.28 138.36)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8700.18, -4884.75)" d="M 8688 4883 C 8694 4881 8706 4881 8713 4883 C 8719 4886 8714 4888 8700 4888 C 8686 4888 8681 4886 8688 4883 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 417.29 145.64)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8715.25, -4830.18)" d="M 8712 4830 C 8712 4816 8714 4811 8717 4818 C 8719 4824 8719 4836 8717 4843 C 8714 4849 8712 4844 8712 4830 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 268.62 147.76)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7600.18, -4814.25)" d="M 7558 4813 C 7581 4811 7619 4811 7643 4813 C 7666 4815 7647 4817 7600 4817 C 7553 4817 7534 4815 7558 4813 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 294.8 147.75)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7796.52, -4814.38)" d="M 7778 4813 C 7790 4811 7808 4811 7818 4813 C 7827 4816 7817 4818 7795 4817 C 7773 4817 7765 4815 7778 4813 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 311.28 147.76)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7920.18, -4814.25)" d="M 7878 4813 C 7901 4811 7939 4811 7963 4813 C 7986 4815 7967 4817 7920 4817 C 7873 4817 7854 4815 7878 4813 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -342.81 164.92)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3014.5, -4685.6)" d="M 2412 4688 L 2415 4635 L 2955 4632 C 3252 4630 3523 4632 3558 4635 L 3620 4642 L 3620 4691 L 3620 4740 L 3014 4740 L 2409 4740 L 2412 4688 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 337.93 165.66)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8120, -4680)" d="M 7520 4680 L 7520 4630 L 8120 4630 L 8720 4630 L 8720 4680 L 8720 4730 L 8120 4730 L 7520 4730 L 7520 4680 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -397.56 296.68)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-2603.87, -3697.36)" d="M 2393 4153 L 2300 4149 L 2300 3694 L 2300 3239 L 2513 3242 C 2754 3245 2770 3250 2846 3332 C 2934 3428 2927 3607 2831 3694 L 2810 3713 L 2834 3739 C 2913 3821 2907 3978 2823 4066 C 2747 4143 2647 4164 2393 4153 z M 2640 3959 C 2676 3946 2700 3903 2692 3865 C 2684 3820 2649 3800 2571 3793 L 2500 3787 L 2500 3879 L 2500 3970 L 2554 3970 C 2583 3970 2622 3965 2640 3959 z M 2682 3580 C 2710 3546 2706 3479 2674 3449 C 2653 3429 2636 3425 2574 3422 L 2500 3418 L 2500 3513 L 2500 3609 L 2533 3612 C 2597 3618 2662 3604 2682 3580 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -226.07 296.62)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3890, -3697.8)" d="M 3797 4153 C 3793 4150 3790 3943 3790 3695 L 3790 3242 L 3883 3238 C 3933 3235 3978 3235 3983 3237 C 3987 3239 3990 3448 3990 3701 L 3990 4160 L 3897 4160 C 3845 4160 3800 4157 3797 4153 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -153.41 296.63)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4435, -3697.74)" d="M 4130 4070 L 4130 3980 L 4230 3980 L 4330 3980 L 4330 3611 C 4330 3325 4333 3241 4343 3238 C 4358 3233 4520 3236 4525 3242 C 4528 3244 4530 3402 4530 3591 C 4531 3781 4534 3946 4537 3958 C 4542 3979 4548 3980 4641 3980 L 4740 3980 L 4740 4070 L 4740 4160 L 4435 4160 L 4130 4160 L 4130 4070 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -55.07 297.2)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5172.5, -3693.5)" d="M 4860 3817 C 4860 3421 4863 3402 4935 3322 C 4998 3252 5061 3226 5170 3226 C 5276 3226 5340 3251 5403 3319 C 5483 3404 5485 3416 5485 3810 L 5485 4155 L 5388 4158 L 5290 4161 L 5290 3835 C 5290 3485 5286 3455 5239 3429 C 5209 3413 5145 3414 5114 3431 C 5062 3458 5060 3473 5060 3830 L 5060 4160 L 4960 4160 L 4860 4160 L 4860 3817 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 45.33 296.33)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5925.5, -3700)" d="M 5650 3700 L 5650 3240 L 5925 3240 L 6201 3240 L 6198 3331 L 6195 3423 L 6023 3422 L 5850 3420 L 5850 3768 C 5850 3959 5847 4125 5844 4138 C 5838 4159 5833 4160 5744 4160 L 5650 4160 L 5650 3700 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 188.59 296.26)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7000, -3700.5)" d="M 6620 3700 L 6620 3240 L 6715 3240 L 6810 3240 L 6810 3448 C 6811 3683 6808 3681 6881 3503 L 6922 3400 L 6995 3400 L 7068 3400 L 7115 3520 C 7140 3586 7166 3642 7171 3645 C 7176 3649 7180 3570 7180 3446 L 7180 3240 L 7280 3240 L 7380 3240 L 7380 3699 L 7380 4157 L 7290 4159 L 7200 4161 L 7147 4048 C 7118 3986 7074 3889 7050 3833 C 7025 3776 7002 3730 6999 3730 C 6996 3730 6954 3821 6905 3933 C 6857 4044 6812 4141 6807 4148 C 6801 4156 6768 4160 6709 4160 L 6620 4160 L 6620 3700 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 301.81 296.57)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7849.12, -3698.2)" d="M 7772 4151 C 7765 4147 7741 4093 7719 4031 C 7665 3876 7470 3262 7470 3245 C 7470 3242 7517 3240 7573 3240 L 7677 3240 L 7699 3315 L 7721 3390 L 7843 3390 L 7966 3390 L 7990 3315 L 8014 3240 L 8123 3240 C 8206 3240 8231 3243 8228 3253 C 8225 3259 8157 3464 8075 3708 L 7928 4150 L 7870 4150 C 7838 4150 7806 4152 7798 4155 C 7791 4158 7779 4156 7772 4151 z M 7910 3573 C 7910 3564 7890 3560 7845 3560 C 7775 3560 7772 3563 7789 3616 C 7795 3632 7809 3679 7821 3720 L 7843 3795 L 7877 3690 C 7895 3632 7910 3579 7910 3573 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -300.79 297.06)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3329.59, -3694.5)" d="M 3218 4043 C 3198 3983 3135 3798 3078 3630 C 3020 3462 2969 3306 2963 3282 L 2952 3239 L 3058 3242 L 3164 3245 L 3188 3318 L 3213 3390 L 3335 3390 L 3456 3390 L 3482 3318 L 3507 3245 L 3609 3242 C 3704 3240 3711 3241 3706 3257 C 3663 3401 3593 3620 3515 3858 L 3419 4150 L 3337 4150 L 3254 4150 L 3218 4043 z M 3370 3685 C 3413 3559 3413 3560 3335 3560 C 3266 3560 3265 3561 3279 3606 C 3285 3622 3298 3668 3310 3708 C 3322 3749 3333 3780 3336 3778 C 3338 3775 3354 3733 3370 3685 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 401.33 297)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8595.5, -3695)" d="M 8300 4027 C 8300 3959 8303 3754 8307 3572 L 8313 3240 L 8602 3240 L 8891 3240 L 8888 3333 L 8885 3425 L 8695 3425 L 8505 3425 L 8502 3788 L 8500 4150 L 8400 4150 L 8300 4150 L 8300 4027 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -407.93 435.21)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-2526.05, -2658.41)" d="M 2473 3030 C 2450 3025 2418 3014 2402 3006 C 2360 2984 2305 2918 2292 2871 C 2277 2824 2276 2490 2290 2439 C 2305 2386 2362 2326 2422 2300 C 2486 2273 2573 2276 2636 2308 C 2683 2332 2736 2389 2756 2436 C 2782 2497 2779 2500 2687 2500 C 2606 2500 2605 2500 2592 2471 C 2574 2431 2530 2413 2489 2430 C 2472 2437 2454 2454 2449 2467 C 2444 2480 2440 2570 2440 2666 C 2440 2838 2440 2841 2465 2865 C 2481 2882 2500 2890 2525 2890 C 2566 2890 2610 2854 2610 2821 C 2610 2792 2618 2790 2699 2797 L 2772 2803 L 2767 2843 C 2750 2968 2608 3058 2473 3030 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -352.74 435.06)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-2940, -2659.5)" d="M 2867 3023 C 2863 3020 2860 2853 2860 2653 L 2860 2289 L 2938 2292 L 3015 2295 L 3018 2663 L 3020 3030 L 2947 3030 C 2906 3030 2870 3027 2867 3023 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -293.07 435.73)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3387.5, -2654.49)" d="M 3270 3014 C 3224 2994 3193 2964 3167 2915 C 3146 2878 3145 2861 3145 2655 C 3145 2407 3147 2400 3222 2336 C 3344 2231 3546 2280 3612 2429 C 3639 2493 3633 2500 3547 2500 C 3474 2500 3472 2499 3460 2471 C 3436 2412 3358 2406 3318 2461 C 3300 2484 3299 2504 3299 2661 C 3300 2868 3306 2883 3383 2888 C 3426 2891 3431 2889 3450 2858 C 3461 2840 3470 2817 3470 2808 C 3470 2788 3490 2786 3578 2796 C 3624 2802 3630 2806 3630 2826 C 3630 2860 3596 2932 3564 2964 C 3517 3011 3465 3030 3380 3029 C 3335 3029 3291 3023 3270 3014 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -214.09 435.59)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3979.9, -2655.54)" d="M 3734 3016 C 3731 3007 3730 2880 3732 2733 C 3735 2488 3737 2461 3756 2420 C 3799 2325 3895 2273 4006 2283 C 4133 2295 4204 2363 4221 2487 C 4230 2560 4232 2956 4223 3003 C 4218 3031 4217 3031 4146 3028 L 4075 3025 L 4070 2755 C 4067 2607 4061 2477 4056 2467 C 4033 2419 3951 2410 3914 2452 C 3897 2470 3895 2498 3890 2749 L 3885 3025 L 3812 3028 C 3755 3030 3739 3028 3734 3016 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -128.07 435)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4625, -2660)" d="M 4370 2660 L 4370 2290 L 4450 2290 L 4530 2290 L 4530 2430 C 4530 2562 4531 2570 4549 2570 C 4574 2570 4595 2539 4664 2403 L 4720 2290 L 4800 2290 C 4844 2290 4880 2294 4880 2299 C 4880 2304 4849 2374 4811 2455 L 4741 2601 L 4775 2623 C 4881 2692 4890 2889 4791 2973 C 4739 3017 4674 3030 4513 3030 L 4370 3030 L 4370 2660 z M 4660 2881 C 4675 2877 4692 2860 4703 2838 C 4719 2805 4720 2798 4706 2767 C 4687 2721 4665 2710 4591 2710 L 4530 2710 L 4530 2793 C 4530 2839 4533 2880 4537 2883 C 4545 2892 4627 2890 4660 2881 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 -48.41 435.46)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5222.5, -2656.54)" d="M 4980 2757 C 4980 2447 4985 2418 5050 2351 C 5141 2258 5312 2261 5401 2357 C 5464 2425 5465 2428 5465 2740 L 5465 3025 L 5390 3025 L 5315 3025 L 5310 2750 C 5307 2599 5301 2468 5297 2459 C 5288 2440 5248 2420 5220 2420 C 5209 2420 5186 2433 5168 2449 L 5135 2478 L 5132 2754 L 5129 3030 L 5054 3030 L 4980 3030 L 4980 2757 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 33.32 435.2)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5835.43, -2658.49)" d="M 5732 3019 C 5682 3004 5614 2933 5600 2881 C 5592 2851 5589 2772 5592 2646 C 5595 2473 5597 2451 5617 2414 C 5660 2334 5737 2289 5835 2288 C 5906 2287 5961 2308 6009 2353 C 6067 2408 6080 2449 6080 2583 L 6080 2700 L 5955 2700 L 5830 2700 L 5830 2641 C 5830 2576 5836 2570 5896 2570 C 5927 2570 5930 2567 5930 2541 C 5930 2500 5911 2451 5890 2440 C 5865 2427 5814 2427 5788 2441 C 5751 2461 5740 2510 5740 2663 C 5740 2742 5745 2821 5750 2837 C 5762 2869 5795 2890 5834 2890 C 5862 2890 5906 2857 5915 2830 C 5921 2812 5930 2810 6000 2810 L 6080 2810 L 6073 2843 C 6051 2959 5958 3030 5830 3029 C 5794 3029 5750 3024 5732 3019 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 153.89 435)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-6739.73, -2660)" d="M 6500 2660 L 6500 2290 L 6658 2290 C 6802 2290 6819 2292 6865 2314 C 6923 2341 6943 2361 6965 2413 C 6995 2486 6977 2590 6923 2652 C 6909 2667 6910 2672 6929 2698 C 6988 2782 6975 2909 6899 2973 C 6845 3020 6797 3030 6636 3030 L 6500 3030 L 6500 2660 z M 6796 2865 C 6804 2855 6810 2829 6810 2809 C 6810 2756 6775 2730 6704 2730 L 6650 2730 L 6650 2803 C 6650 2844 6653 2880 6658 2884 C 6662 2888 6691 2890 6724 2888 C 6766 2885 6786 2878 6796 2865 z M 6785 2584 C 6804 2576 6830 2529 6830 2500 C 6830 2489 6819 2468 6805 2455 C 6785 2434 6771 2430 6720 2430 L 6660 2430 L 6660 2510 L 6660 2590 L 6714 2590 C 6744 2590 6776 2587 6785 2584 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 237.13 435)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7364, -2660)" d="M 7291 3013 C 7277 2985 7070 2325 7070 2307 C 7070 2292 7082 2290 7150 2290 C 7210 2290 7230 2293 7230 2303 C 7230 2310 7237 2340 7245 2368 L 7261 2420 L 7349 2420 C 7408 2420 7441 2416 7447 2408 C 7453 2401 7465 2371 7474 2343 L 7492 2290 L 7575 2290 L 7658 2290 L 7638 2353 C 7627 2387 7597 2480 7573 2560 C 7548 2640 7503 2778 7473 2868 L 7417 3030 L 7359 3030 C 7314 3030 7299 3026 7291 3013 z M 7384 2646 C 7398 2600 7410 2559 7410 2556 C 7410 2547 7310 2549 7310 2558 C 7310 2569 7353 2730 7356 2730 C 7357 2730 7370 2692 7384 2646 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 310.59 435)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7915, -2660)" d="M 7670 2955 L 7670 2880 L 7750 2880 L 7830 2880 L 7830 2585 L 7830 2290 L 7908 2290 L 7985 2290 L 7985 2587 L 7985 2885 L 8073 2884 L 8160 2883 L 8160 2957 L 8160 3030 L 7915 3030 L 7670 3030 L 7670 2955 z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.13 0 0 -0.13 385.93 434.93)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8480, -2660.5)" d="M 8365 2913 C 8345 2848 8311 2741 8290 2675 C 8241 2526 8180 2320 8180 2303 C 8180 2294 8202 2290 8258 2290 L 8336 2290 L 8356 2353 L 8376 2415 L 8467 2418 C 8569 2421 8569 2421 8594 2343 L 8612 2290 L 8696 2290 C 8756 2290 8780 2293 8780 2302 C 8780 2309 8760 2371 8735 2440 C 8710 2509 8655 2669 8612 2795 L 8535 3025 L 8468 3028 L 8401 3031 L 8365 2913 z M 8494 2656 C 8508 2610 8520 2567 8520 2561 C 8520 2549 8420 2545 8420 2558 C 8419 2567 8464 2740 8467 2740 C 8468 2740 8480 2702 8494 2656 z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>