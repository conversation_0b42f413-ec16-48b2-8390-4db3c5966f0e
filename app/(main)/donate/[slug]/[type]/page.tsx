'use client';

import { IAccount } from '@/models/account';
import { IDonate } from '@/models/donate';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { isEmpty } from 'lodash';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { InputSwitch } from 'primereact/inputswitch';
import { InputTextarea } from 'primereact/inputtextarea';
import { Toast } from 'primereact/toast';
import { useEffect, useMemo, useRef, useState } from 'react';

dayjs.locale('id');
dayjs.extend(customParseFormat);

const FormDonate = ({ params }: { params: Promise<{ slug: string; type: string }> }) => {
    const [action, setAction] = useState('baru');
    const [type, setType] = useState('infaq');
    const [info, setInfo] = useState('');
    const [amount, setAmount] = useState(0);
    const [account, setAccount] = useState<{ code: string; name: string } | undefined>();
    const [accounts, setAccounts] = useState<{ code: string; name: string }[]>([]);
    const [date, setDate] = useState<any>(dayjs().toDate());
    const [isRemoval, setRemoval] = useState(false);
    const [isLoad, setLoad] = useState(false);

    const router = useRouter();
    const { data: session } = useSession();
    const toast = useRef<Toast>(null);
    const mapTypes = useMemo<{ [key: string]: string }>(() => ({ infaq: 'Pemberian Infaq', manfaat: 'Penggunaan Infaq' }), []);

    const fetching = async (id: string) => {
        try {
            const results = await fetch('/api/account', { method: 'GET', headers: { 'Content-Type': 'application/json' }, next: { revalidate: 60 } });
            const response = await fetch(`/api/donate/${id}`, { method: 'GET', headers: { 'Content-Type': 'application/json' }, next: { revalidate: 60 } });
            const donation: IDonate = await response.json();
            const accounts: IAccount[] = await results.json();
            const customer = accounts.find(({ id }) => id === donation?.account || '');
            setAccounts(accounts.map(({ id, name }) => ({ code: id, name })));

            if (donation) {
                setAction(donation.id);
                setAccount(customer ? { code: customer.id, name: customer.name } : undefined);
                setDate(donation?.date ? dayjs(donation.date, 'DD-MM-YYYY').toDate() : dayjs().toDate());
                setType(donation.type);
                setInfo(donation.info);
                setAmount(donation.amount);
            }
        } catch (_) {
            toast.current?.show({
                life: 3000,
                severity: 'warn',
                summary: 'Gagal memuat!',
                detail: 'Data tidak dapat diambil atau tidak ditemukan'
            });
        }
    };

    useEffect(() => {
        const setFormAction = async () => {
            try {
                const { slug, type } = await params;
                setType(type);
                await fetching(slug);
            } catch (_) {}
        };

        setFormAction();
    }, [params]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <Toast ref={toast} />
                    <h5>
                        {action === 'baru' ? 'Tambah' : 'Ubah'} Transaksi {mapTypes[type]}
                    </h5>
                    <div className="p-fluid">
                        <div className="field">
                            <label htmlFor="date">Tanggal</label>
                            <Calendar showIcon showButtonBar dateFormat="dd MM yy" id="date" value={date} onChange={(e) => setDate(e.value ?? null)} />
                        </div>
                        <div className="field">
                            <label htmlFor="amount">Nominal</label>
                            <InputNumber id="amount" placeholder="Nominal" value={amount} onValueChange={(e) => setAmount(e.value || 0)} min={0} maxFractionDigits={0} mode="currency" currency="IDR" />
                        </div>
                        <div className="field">
                            <label htmlFor="account">Anggota / Nasabah</label>
                            <Dropdown
                                id="account"
                                value={account}
                                onChange={(e) => setAccount(e.value)}
                                options={accounts}
                                optionLabel="name"
                                placeholder={`${type === 'infaq' ? 'Dari' : 'Untuk'} Anggota/Nasabah`}
                                showClear={!isEmpty(account)}
                                filter
                            />
                        </div>
                        <div className="field">
                            <label htmlFor="info">Keterangan</label>
                            <InputTextarea id="info" placeholder="Isikan keterangan transaksi ( jika diperlukan )" rows={2} autoResize value={info} onChange={(e) => setInfo(e.target.value)} />
                        </div>
                        {action !== 'baru' && (
                            <div className="field">
                                <label htmlFor="removal">Hapus data ini?</label> <br />
                                <InputSwitch id="removal" checked={isRemoval} onChange={(e) => setRemoval(e.value)} />
                            </div>
                        )}
                    </div>
                    <hr />
                    <div className="flex justify-content-between flex-wrap">
                        <Button label="Batal" icon="pi pi-times" severity="info" onClick={() => router.back()} />
                        {!isLoad && (
                            <Button
                                label="Simpan"
                                icon="pi pi-check"
                                className="form-action-button"
                                onClick={async () => {
                                    setLoad(true);
                                    const response = await fetch('/api/donate', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({
                                            action,
                                            type,
                                            amount,
                                            info: info || `${mapTypes[type]}`,
                                            date: dayjs(date).format('DD-MM-YYYY'),
                                            operator: session?.user?.email,
                                            account: account?.code || '',
                                            removal: isRemoval ?? false
                                        })
                                    });
                                    const result = await response.json();
                                    setLoad(false);

                                    if (result?.saved) {
                                        router.back();
                                    } else {
                                        toast.current?.show({
                                            life: 3000,
                                            severity: 'warn',
                                            summary: 'Gagal simpan!',
                                            detail: 'Data tidak dapat disimpan oleh Sistem'
                                        });
                                    }
                                }}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FormDonate;
