'use client';

import { exportExcel, exportPdf } from '@/lib/function';
import { IAccount } from '@/models/account';
import { useRouter } from 'next/navigation';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Menu } from 'primereact/menu';
import { Tag } from 'primereact/tag';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const TableAccount = () => {
    const [list, setList] = useState<IAccount[]>([]);
    const [loading, setLoading] = useState(true);
    const [active, setActive] = useState(0);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');

    const router = useRouter();
    const menuExport = useRef<Menu>(null);
    const dataTable = useRef(null);
    const statusBodyTemplate = (rowData: IAccount) => <Tag value={rowData.status ? 'AKTIF' : 'TIDAK AKTIF'} severity={rowData.status ? 'success' : 'warning'} />;
    const editBodyTemplate = (rowData: IAccount) => <Button icon="pi pi-pencil" outlined onClick={() => router.push(`/account/${rowData.id}`)} />;

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const fetching = useCallback(async () => {
        try {
            const response = await fetch('/api/account', { method: 'GET', headers: { 'Content-Type': 'application/json' }, next: { revalidate: 60 } });
            const list: IAccount[] = await response.json();
            setList(list);
            setActive(list.filter(({ status }) => !!status).length);
        } catch (_) {
            console.error('error on account page', _);
        }

        setLoading(false);
        initFilters();
    }, []);

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const renderHeader = () => {
        return (
            <div className="flex justify-content-between flex-wrap">
                <Button type="button" icon="pi pi-users" label="Tambah" outlined onClick={() => router.push('/account/baru')} />
                <span className="p-input-icon-left filter-input–table">
                    <i className="pi pi-search" />
                    <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
                </span>
            </div>
        );
    };

    useEffect(() => {
        setLoading(true);
        fetching();
    }, [fetching]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="flex justify-content-between align-items-center">
                        <h5>
                            Data Nasabah / Anggota ({active} - {list.length})
                        </h5>
                        <div>
                            <Button type="button" icon="pi pi-file-export" rounded text className="p-button-plain" onClick={(event) => menuExport.current?.toggle(event)} />
                            <Menu
                                popup
                                ref={menuExport}
                                model={[
                                    { label: 'PDF', icon: 'pi pi-fw pi-file-pdf', command: () => exportPdf(list, 'account') },
                                    { label: 'Sheet', icon: 'pi pi-fw pi-file-excel', command: () => exportExcel(list, 'account') }
                                ]}
                            />
                        </div>
                    </div>
                    <DataTable
                        ref={dataTable}
                        className="p-datatable-gridlines"
                        header={renderHeader}
                        loading={loading}
                        filters={filters}
                        value={list}
                        rows={10}
                        dataKey="id"
                        filterDisplay="menu"
                        emptyMessage="Tidak ditemukan data anggota!"
                        paginator
                        showGridlines
                        stripedRows
                        scrollable
                    >
                        <Column field="number" header="No Anggota" />
                        <Column field="name" header="Nama" />
                        <Column field="area" header="Area (RT)" />
                        <Column field="status" header="Status" body={statusBodyTemplate} />
                        <Column header="" body={editBodyTemplate} className="filter-action-button" />
                    </DataTable>
                </div>
            </div>
        </div>
    );
};

export default TableAccount;
