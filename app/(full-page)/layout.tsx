import { Metadata } from 'next';
import React from 'react';

interface SimpleLayoutProps {
    children: React.ReactNode;
}

export const metadata: Metadata = {
    title: 'Baitul Mal Cicurug Bata',
    description: 'Baitul Mal Cicurug Bata Tasikmalaya.',
    robots: { index: false, follow: false },
    viewport: { initialScale: 1, width: 'device-width' },
    openGraph: {
        type: 'website',
        title: 'Baitul Mal Cicurug Bata',
        url: 'https://bcbs-dashboard-v2.vercel.app/',
        description: 'Baitul Mal Cicurug Bata Tasikmalaya.',
        images: ['https://bcbs-dashboard-v2.vercel.app/images/layout/icon-512x512.png'],
        ttl: 604800
    },
    icons: { icon: '/favicon.ico' }
};

export default function SimpleLayout({ children }: SimpleLayoutProps) {
    return <React.Fragment>{children}</React.Fragment>;
}
