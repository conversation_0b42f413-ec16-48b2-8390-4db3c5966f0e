/* eslint-disable @next/next/no-img-element */
'use client';

import { LayoutContext } from '@/layout/context/layoutcontext';
import { isEmpty } from 'lodash';
import { signIn, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { Skeleton } from 'primereact/skeleton';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { useContext, useEffect, useRef, useState } from 'react';

const LoginPage = () => {
    const [password, setPassword] = useState('');
    const [phone, setPhone] = useState('');
    const [isLoad, setIsLoad] = useState(false);
    const [showInstallLink, setShowInstallLink] = useState(false);
    const [prompt, setPrompt] = useState<any>(null);
    const { layoutConfig } = useContext(LayoutContext);

    const router = useRouter();
    const { data: session, status: authorizing } = useSession();
    const toast = useRef<Toast>(null);
    const containerClassName = classNames('surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden', { 'p-input-filled': layoutConfig.inputStyle === 'filled' });

    const doSign = async () => {
        let showToast = false;
        let detail = '';

        try {
            if (isEmpty(phone)) {
                showToast = true;
                detail = 'Nomor telepon tidak boleh kosong';
            }

            if (!showToast && isEmpty(password)) {
                showToast = true;
                detail = 'Kata sandi tidak boleh kosong';
            }

            if (!showToast) {
                const response = await signIn('credentials', { phone, password, redirect: false });

                if (!response?.ok) {
                    toast.current?.show({
                        life: 3000,
                        severity: 'error',
                        summary: 'Akses ditolak!',
                        detail: 'Akun tidak ditemukan'
                    });
                } else {
                    router.push('/dashboard');
                }
            } else {
                toast.current?.show({
                    life: 3000,
                    severity: 'warn',
                    summary: 'Validasi gagal!',
                    detail
                });
            }
        } catch (_) {
            console.error('error on auth page', _);
        }

        setIsLoad(false);
    };

    useEffect(() => {
        if (session) {
            router.push('/dashboard');
        }
    }, [router, session]);

    useEffect(() => {
        const handleBeforeInstallPrompt = (e: any) => {
            e.preventDefault();
            setPrompt(e);

            if (!window.matchMedia('(display-mode: standalone)').matches) {
                setShowInstallLink(true);
            }
        };

        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

        return () => {
            window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
        };
    }, []);

    return (
        <div className={containerClassName}>
            <div className="flex flex-column align-items-center justify-content-center">
                <img src={`/images/layout/logo-${layoutConfig.colorScheme === 'light' ? 'dark' : 'white'}.svg`} alt="Logo" className="mb-5 w-6rem flex-shrink-0" />
                <div
                    style={{
                        borderRadius: '56px',
                        padding: '0.3rem',
                        background: 'linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)'
                    }}
                >
                    <div className="w-full surface-card py-8 px-5 sm:px-8" style={{ borderRadius: '53px' }}>
                        <Toast ref={toast} />

                        <div className="text-center mb-5">
                            <img src="/images/layout/bcbs.png" alt="BCBS" height="170" className="mb-3 border-round-2xl" />
                            <div className="text-900 text-3xl font-medium mb-3">Dashboard BCBS</div>
                            <span className="text-600 font-medium">Baitul Mal | Cicurug Bata</span>
                        </div>

                        {authorizing === 'loading' && <Skeleton width="30em" height="20em" />}

                        {authorizing === 'unauthenticated' && (
                            <div>
                                <label htmlFor="phone" className="block text-900 text-xl font-medium mb-2">
                                    Telepon
                                </label>
                                <InputText id="phone" type="tel" placeholder="Nomor Telepon" className="w-full md:w-30rem mb-5" style={{ padding: '1rem' }} value={phone} onChange={(e) => setPhone(e.target.value)} />

                                <label htmlFor="password" className="block text-900 font-medium text-xl mb-2">
                                    Sandi
                                </label>
                                <Password inputId="password" value={password} onChange={(e) => setPassword(e.target.value)} placeholder="Kata Sandi" toggleMask className="w-full mb-5" inputClassName="w-full p-3 md:w-30rem"></Password>

                                {showInstallLink && (
                                    <div className="flex align-items-center justify-content-between mb-5 gap-5">
                                        <Button
                                            link
                                            text
                                            className="font-medium no-underline ml-2 text-right cursor-pointer"
                                            label="Install Web App"
                                            style={{ color: 'var(--primary-color)' }}
                                            onClick={() => {
                                                if (prompt) {
                                                    prompt.prompt();
                                                    prompt.useChoice.then((_: any) => {
                                                        setPrompt(null);
                                                    });
                                                }
                                            }}
                                        />
                                    </div>
                                )}

                                <div className="flex align-items-center justify-content-between mb-5 gap-5"></div>
                                {!isLoad && (
                                    <Button
                                        label="Akses"
                                        className="w-full p-3 text-xl"
                                        onClick={async () => {
                                            setIsLoad(true);
                                            await doSign();
                                        }}
                                    />
                                )}
                                {isLoad && (
                                    <Button
                                        label="Proses..."
                                        className="w-full p-3 text-xl"
                                        onClick={() => {
                                            toast.current?.show({
                                                life: 3000,
                                                severity: 'info',
                                                summary: 'Info',
                                                detail: 'Masih memproses otentikasi hak akses Anda...'
                                            });
                                        }}
                                    />
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;
