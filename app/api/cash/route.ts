import connectDB from '@/lib/mongo';
import cash, { ICash } from '@/models/cash';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response = Response.json({ error: 'Unprocessable operation!' }, { status: 422 });

    try {
        await connectDB();
        const cashes = await cash.find().sort({ created: 'desc' }).lean<ICash[]>();
        response = Response.json(cashes, { status: 200 });
    } catch (error) {
        const message = error instanceof Error ? error.message : 'Server error';
        response = Response.json({ error: message }, { status: 500 });
    }

    return response;
}

export const revalidate = 0; // seconds
