import connectDB from '@/lib/mongo';
import admin, { IAdmin } from '@/models/admin';
import donate, { IDonate } from '@/models/donate';
import dayjs from 'dayjs';
import dayjsUTC from 'dayjs/plugin/utc';
import { isEmpty } from 'lodash';
import { revalidatePath } from 'next/cache';
import { NextRequest } from 'next/server';
import { v4 as uuid } from 'uuid';

dayjs.extend(dayjsUTC);

export async function GET(_: NextRequest) {
    let response: Response = Response.json({ error: 'Unprocessable operation!' }, { status: 422 });

    try {
        await connectDB();
        const donates = await donate.find().sort({ created: 'desc' }).lean<IDonate[]>();
        response = Response.json(donates, { status: 200 });
    } catch (error) {
        const message = error instanceof Error ? error.message : 'Server error';
        response = Response.json({ error: message }, { status: 500 });
    }

    return response;
}

export async function POST(request: NextRequest) {
    let response: Response = Response.json({ error: 'Unprocessable operation!' }, { status: 422 });

    try {
        await connectDB();
        let saved: IDonate | null = null;
        const params = await request.json();
        const id = params?.action === 'baru' ? uuid() : params?.action;
        const flagAsDelete = params?.removal ?? false;

        if (!isEmpty(params?.date) && !isEmpty(params?.type) && !isEmpty(params?.info) && params?.amount > 0) {
            let operator: IAdmin | null = null;

            if (!isEmpty(params?.operator)) {
                operator = await admin.findOne({ phone: params.operator }).lean<IAdmin>();
            }

            if (!flagAsDelete) {
                saved = await donate
                    .findOneAndUpdate(
                        { id },
                        {
                            date: params.date,
                            type: params.type,
                            info: params.info,
                            amount: params.amount,
                            account: params?.account || '',
                            operator: operator?.id || 'system',
                            ...(params?.action === 'baru' ? { created: dayjs().utc().toDate() } : { updated: dayjs().utc().toDate() })
                        },
                        { upsert: true, new: true, lean: true }
                    )
                    .lean<IDonate>();
            } else {
                saved = await donate.findOneAndDelete({ id }, { lean: true }).lean<IDonate>();
            }
        }

        revalidatePath('/dashboard');
        revalidatePath('/report');
        response = Response.json({ saved: !saved?._id ? false : true }, { status: 200 });
    } catch (error) {
        const message = error instanceof Error ? error.message : 'Server error';
        response = Response.json({ error: message, saved: false }, { status: 500 });
    }

    return response;
}

export const revalidate = 0; // seconds
