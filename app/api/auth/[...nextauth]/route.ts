import { IAdmin } from '@/models/admin';
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

const handler = NextAuth({
    providers: [
        CredentialsProvider({
            name: 'Credentials',
            credentials: { phone: { label: 'Nomor Telepon', type: 'text' }, password: { label: 'Kat<PERSON> Sandi', type: 'text' } },
            async authorize(credentials) {
                const response = await fetch(`${process.env.NEXTAUTH_URL}/api/login`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ phone: credentials?.phone, password: credentials?.password }) });
                const user: IAdmin = await response.json();

                return response.ok && user ? { id: user.id, name: user.name, email: user.phone } : null;
            }
        })
    ],
    pages: { signIn: '/auth' }
});

export { handler as GET, handler as POST };
