{"name": "bcbs-dashboard", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"dev": "npm i && npm run format && npm run lint && next dev", "build": "next build", "start": "next start", "format": "prettier --check --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint"}, "dependencies": {"@types/node": "^22.14.0", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "@vercel/analytics": "^1.5.0", "chart.js": "4.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "mongoose": "^8.13.1", "next": "13.4.8", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "10.2.1", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "^5.8.3", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/mongoose": "^5.11.97", "@types/next-pwa": "^5.6.9", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}